<?php
/**
 * PayOp Plugin Test Script
 * 
 * This script tests the critical fixes we've implemented
 * Run this from WordPress admin or via WP-CLI
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit('Direct access not allowed');
}

class PayOp_Test_Fixes {
    
    private $results = array();
    
    public function run_tests() {
        echo "<h2>PayOp Plugin Fix Verification</h2>\n";
        
        $this->test_api_client_instantiation();
        $this->test_payment_methods_loading();
        $this->test_blocks_integration();
        $this->test_admin_functionality();
        $this->test_security_fixes();
        
        $this->display_results();
    }
    
    private function test_api_client_instantiation() {
        echo "<h3>1. Testing API Client Instantiation</h3>\n";
        
        try {
            $api_client = new PayOp_API_Client('test-key', 'test-secret', 'test-jwt', false);
            $this->results['api_client'] = array(
                'status' => 'PASS',
                'message' => 'API Client instantiated successfully'
            );
            echo "✅ API Client instantiation: PASS\n";
        } catch (Exception $e) {
            $this->results['api_client'] = array(
                'status' => 'FAIL',
                'message' => 'API Client instantiation failed: ' . $e->getMessage()
            );
            echo "❌ API Client instantiation: FAIL - " . $e->getMessage() . "\n";
        }
    }
    
    private function test_payment_methods_loading() {
        echo "<h3>2. Testing Payment Methods Loading</h3>\n";
        
        try {
            // Test with mock API client
            $api_client = new PayOp_API_Client('test-key', 'test-secret', 'test-jwt', true);
            $payment_methods = new PayOp_Payment_Methods($api_client);
            
            // This should not throw an error even if API fails
            $methods = $payment_methods->get_available_methods('USD', 'US');
            
            $this->results['payment_methods'] = array(
                'status' => 'PASS',
                'message' => 'Payment methods manager works correctly'
            );
            echo "✅ Payment Methods Loading: PASS\n";
        } catch (Exception $e) {
            $this->results['payment_methods'] = array(
                'status' => 'FAIL',
                'message' => 'Payment methods loading failed: ' . $e->getMessage()
            );
            echo "❌ Payment Methods Loading: FAIL - " . $e->getMessage() . "\n";
        }
    }
    
    private function test_blocks_integration() {
        echo "<h3>3. Testing WooCommerce Blocks Integration</h3>\n";
        
        try {
            if (class_exists('PayOp_Blocks_Integration')) {
                $blocks_integration = new PayOp_Blocks_Integration();
                
                $this->results['blocks_integration'] = array(
                    'status' => 'PASS',
                    'message' => 'Blocks integration class exists and instantiates'
                );
                echo "✅ Blocks Integration: PASS\n";
            } else {
                throw new Exception('PayOp_Blocks_Integration class not found');
            }
        } catch (Exception $e) {
            $this->results['blocks_integration'] = array(
                'status' => 'FAIL',
                'message' => 'Blocks integration failed: ' . $e->getMessage()
            );
            echo "❌ Blocks Integration: FAIL - " . $e->getMessage() . "\n";
        }
    }
    
    private function test_admin_functionality() {
        echo "<h3>4. Testing Admin Functionality</h3>\n";
        
        try {
            if (class_exists('PayOp_Admin')) {
                $admin = new PayOp_Admin();
                
                $this->results['admin_functionality'] = array(
                    'status' => 'PASS',
                    'message' => 'Admin class exists and instantiates'
                );
                echo "✅ Admin Functionality: PASS\n";
            } else {
                throw new Exception('PayOp_Admin class not found');
            }
        } catch (Exception $e) {
            $this->results['admin_functionality'] = array(
                'status' => 'FAIL',
                'message' => 'Admin functionality failed: ' . $e->getMessage()
            );
            echo "❌ Admin Functionality: FAIL - " . $e->getMessage() . "\n";
        }
    }
    
    private function test_security_fixes() {
        echo "<h3>5. Testing Security Fixes</h3>\n";
        
        $security_tests = array();
        
        // Test 1: Check if exposed credentials are removed from documentation
        $doc_content = file_get_contents(PAYOP_WC_PLUGIN_DIR . 'PayOp_API_Payment_Integration_Documentation.md');
        if (strpos($doc_content, 'fd6d7b9d6e14146ba064cd3b7afd7a0e') === false) {
            $security_tests[] = '✅ Exposed credentials removed from documentation';
        } else {
            $security_tests[] = '❌ Exposed credentials still in documentation';
        }
        
        // Test 2: Check if sanitization methods exist
        if (class_exists('PayOp_WC_Gateway')) {
            $gateway = new PayOp_WC_Gateway();
            if (method_exists($gateway, 'sanitize_customer_data')) {
                $security_tests[] = '✅ Customer data sanitization method exists';
            } else {
                $security_tests[] = '❌ Customer data sanitization method missing';
            }
        }
        
        // Test 3: Check if CSRF protection is in place
        $gateway_content = file_get_contents(PAYOP_WC_PLUGIN_DIR . 'includes/class-payop-gateway.php');
        if (strpos($gateway_content, 'check_ajax_referer') !== false) {
            $security_tests[] = '✅ CSRF protection implemented';
        } else {
            $security_tests[] = '❌ CSRF protection missing';
        }
        
        foreach ($security_tests as $test) {
            echo $test . "\n";
        }
        
        $passed_security = count(array_filter($security_tests, function($test) {
            return strpos($test, '✅') === 0;
        }));
        
        $this->results['security_fixes'] = array(
            'status' => $passed_security >= 2 ? 'PASS' : 'FAIL',
            'message' => "Security tests: {$passed_security}/" . count($security_tests) . " passed"
        );
    }
    
    private function display_results() {
        echo "<h3>Test Summary</h3>\n";
        
        $total_tests = count($this->results);
        $passed_tests = count(array_filter($this->results, function($result) {
            return $result['status'] === 'PASS';
        }));
        
        echo "<p><strong>Overall Result: {$passed_tests}/{$total_tests} tests passed</strong></p>\n";
        
        if ($passed_tests === $total_tests) {
            echo "<div style='color: green; font-weight: bold;'>🎉 All critical issues have been fixed!</div>\n";
        } else {
            echo "<div style='color: red; font-weight: bold;'>⚠️ Some issues still need attention:</div>\n";
            foreach ($this->results as $test => $result) {
                if ($result['status'] === 'FAIL') {
                    echo "<li>{$test}: {$result['message']}</li>\n";
                }
            }
        }
        
        echo "<h4>Next Steps:</h4>\n";
        echo "<ol>\n";
        echo "<li>Configure your PayOp API credentials in WooCommerce Settings</li>\n";
        echo "<li>Test the 'Test Connection' button in PayOp admin page</li>\n";
        echo "<li>Use 'Refresh Methods' to load payment methods from PayOp API</li>\n";
        echo "<li>Test a small transaction to verify payment flow</li>\n";
        echo "<li>Test WooCommerce Blocks checkout if using modern themes</li>\n";
        echo "</ol>\n";
    }
}

// Run tests if accessed directly or via admin
if (is_admin() || (defined('WP_CLI') && WP_CLI)) {
    $tester = new PayOp_Test_Fixes();
    $tester->run_tests();
}
?>
