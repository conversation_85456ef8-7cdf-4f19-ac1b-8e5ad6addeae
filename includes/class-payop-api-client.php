<?php
/**
 * PayOp API Client
 */

if (!defined('ABSPATH')) {
    exit;
}

class PayOp_API_Client {
    
    private $base_url = 'https://api.payop.com';
    private $public_key;
    private $secret_key;
    private $jwt_token;
    private $test_mode;
    
    /**
     * Constructor
     */
    public function __construct($public_key = null, $secret_key = null, $jwt_token = null, $test_mode = false) {
        $this->public_key = $public_key ?: get_option('woocommerce_payop_settings')['public_key'] ?? '';
        $this->secret_key = $secret_key ?: get_option('woocommerce_payop_settings')['secret_key'] ?? '';
        $this->jwt_token = $jwt_token ?: get_option('woocommerce_payop_settings')['jwt_token'] ?? '';
        $this->test_mode = $test_mode ?: (get_option('woocommerce_payop_settings')['test_mode'] ?? 'no') === 'yes';

        // PayOp has no sandbox environment - always use production API
        // Test mode only affects SSL verification for development environments
    }
    
    /**
     * Get available payment methods
     */
    public function get_payment_methods($application_id = 606) {
        $endpoint = '/v1/instrument-settings/payment-methods/available-for-application/' . $application_id;

        return $this->make_request($endpoint, null, 'GET', true);
    }
    
    /**
     * Create invoice
     */
    public function create_invoice($invoice_data) {
        $endpoint = '/v1/invoices/create';
        
        // Add signature
        $invoice_data['signature'] = $this->generate_signature(
            $invoice_data['order']['amount'],
            $invoice_data['order']['currency'],
            $invoice_data['order']['number']
        );
        
        return $this->make_request($endpoint, $invoice_data, 'POST');
    }
    
    /**
     * Get invoice details
     */
    public function get_invoice($invoice_id) {
        $endpoint = '/v1/invoices/' . $invoice_id;
        return $this->make_request($endpoint, null, 'GET');
    }
    
    /**
     * Create checkout transaction
     */
    public function create_checkout($checkout_data) {
        $endpoint = '/v1/checkout/create';
        return $this->make_request($endpoint, $checkout_data, 'POST', true);
    }

    /**
     * Check invoice status
     */
    public function check_invoice_status($invoice_id) {
        $endpoint = '/v1/checkout/check-invoice-status/' . $invoice_id;
        return $this->make_request($endpoint, null, 'GET', true);
    }

    /**
     * Get transaction details
     */
    public function get_transaction($transaction_id) {
        $endpoint = '/v2/transactions/' . $transaction_id;
        return $this->make_request($endpoint, null, 'GET', true);
    }

    /**
     * Void transaction
     */
    public function void_transaction($invoice_id) {
        $endpoint = '/v1/checkout/void';
        $data = array('invoiceIdentifier' => $invoice_id);
        return $this->make_request($endpoint, $data, 'POST', true);
    }

    /**
     * Test API connection
     */
    public function test_connection() {
        try {
            $response = $this->get_payment_methods();

            if ($response && $response['status'] === 1) {
                return array(
                    'success' => true,
                    'message' => 'Connection successful',
                    'data' => $response
                );
            } else {
                return array(
                    'success' => false,
                    'message' => $response['message'] ?? 'Connection failed'
                );
            }
        } catch (Exception $e) {
            return array(
                'success' => false,
                'message' => $e->getMessage()
            );
        }
    }
    
    /**
     * Generate signature for PayOp requests
     */
    public function generate_signature($amount, $currency, $order_id) {
        $string = sprintf('%s:%s:%s:%s', $amount, $currency, $order_id, $this->secret_key);
        return hash('sha256', $string);
    }
    
    /**
     * Make HTTP request to PayOp API
     */
    private function make_request($endpoint, $data = null, $method = 'GET', $require_jwt = false) {
        $url = $this->base_url . $endpoint;

        $headers = array(
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
        );

        // Add JWT token if required or available
        if ($require_jwt || !empty($this->jwt_token)) {
            if (empty($this->jwt_token)) {
                throw new Exception('JWT token is required for this endpoint but not configured');
            }
            $headers['Authorization'] = 'Bearer ' . $this->jwt_token;
        }
        
        $args = array(
            'method' => $method,
            'headers' => $headers,
            'timeout' => 30,
            'sslverify' => !$this->test_mode, // Only disable SSL verification in test mode for development
        );
        
        if ($method === 'GET' && $data) {
            $url .= '?' . http_build_query($data);
        } elseif ($method === 'POST' && $data) {
            $args['body'] = json_encode($data);
        }
        
        $response = wp_remote_request($url, $args);
        
        if (is_wp_error($response)) {
            $this->log_error('HTTP Request Error', $response->get_error_message());
            throw new Exception('API request failed: ' . $response->get_error_message());
        }
        
        $status_code = wp_remote_retrieve_response_code($response);
        $body = wp_remote_retrieve_body($response);
        
        $this->log_debug('API Request', array(
            'url' => $url,
            'method' => $method,
            'status' => $status_code,
            'response' => $body
        ));
        
        if ($status_code >= 400) {
            $error_data = json_decode($body, true);
            $error_message = isset($error_data['message']) ? $error_data['message'] : 'Unknown API error';
            
            $this->log_error('API Error', $error_message, array(
                'status_code' => $status_code,
                'response' => $body
            ));
            
            throw new Exception('PayOp API Error: ' . $error_message, $status_code);
        }
        
        $decoded_response = json_decode($body, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception('Invalid JSON response from PayOp API');
        }
        
        return $decoded_response;
    }
    
    /**
     * Validate IPN source IP
     */
    public function validate_ipn_ip($ip) {
        $valid_ips = array(
            '*************',
            '*************',
            '************',
            '*************'
        );
        
        return in_array($ip, $valid_ips);
    }
    
    /**
     * Log error messages
     */
    private function log_error($context, $message, $data = null) {
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log(sprintf('[PayOp %s] %s', $context, $message));
            if ($data) {
                error_log('[PayOp Data] ' . print_r($data, true));
            }
        }
        
        // Also log to WooCommerce logger if available
        if (function_exists('wc_get_logger')) {
            $logger = wc_get_logger();
            $logger->error($message, array('source' => 'payop', 'context' => $context, 'data' => $data));
        }
    }
    
    /**
     * Log debug messages
     */
    private function log_debug($context, $data) {
        if (defined('WP_DEBUG') && WP_DEBUG && defined('WP_DEBUG_LOG') && WP_DEBUG_LOG) {
            error_log(sprintf('[PayOp %s] %s', $context, print_r($data, true)));
        }
    }
    
    /**
     * Test API connection
     */
    public function test_connection() {
        try {
            $response = $this->get_payment_methods();
            return array(
                'success' => true,
                'message' => 'Connection successful',
                'data' => $response
            );
        } catch (Exception $e) {
            return array(
                'success' => false,
                'message' => $e->getMessage(),
                'data' => null
            );
        }
    }
}
