<?php
/**
 * PayOp WooCommerce Blocks Integration
 */

if (!defined('ABSPATH')) {
    exit;
}

use Automattic\WooCommerce\Blocks\Payments\Integrations\AbstractPaymentMethodType;

/**
 * PayOp payment method integration for WooCommerce Blocks
 */
class PayOp_Blocks_Integration extends AbstractPaymentMethodType {
    
    /**
     * Payment method name
     */
    protected $name = 'payop';
    
    /**
     * PayOp gateway instance
     */
    private $gateway;
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('woocommerce_blocks_loaded', array($this, 'initialize_blocks_integration'));
    }
    
    /**
     * Initialize blocks integration
     */
    public function initialize_blocks_integration() {
        if (class_exists('Automattic\WooCommerce\Blocks\Payments\Integrations\AbstractPaymentMethodType')) {
            add_action('woocommerce_blocks_payment_method_type_registration', array($this, 'register_payment_method'));
            add_action('woocommerce_store_api_checkout_process_payment_with_context', array($this, 'process_payment_with_context'), 10, 2);
        }
    }
    
    /**
     * Register payment method with blocks
     */
    public function register_payment_method($payment_method_registry) {
        $payment_method_registry->register($this);
    }
    
    /**
     * Initialize the payment method
     */
    public function initialize() {
        $this->settings = get_option('woocommerce_payop_settings', array());
        $gateways = WC()->payment_gateways->payment_gateways();
        $this->gateway = $gateways['payop'] ?? null;
    }
    
    /**
     * Check if payment method is active
     */
    public function is_active() {
        return $this->gateway && $this->gateway->is_available();
    }
    
    /**
     * Get payment method script handles
     */
    public function get_payment_method_script_handles() {
        wp_register_script(
            'payop-blocks-integration',
            PAYOP_WC_PLUGIN_URL . 'assets/js/payop-blocks.js',
            array(
                'wc-blocks-registry',
                'wc-settings',
                'wp-element',
                'wp-html-entities',
                'wp-i18n',
            ),
            PAYOP_WC_VERSION,
            true
        );
        
        return array('payop-blocks-integration');
    }
    
    /**
     * Get payment method script handles for admin
     */
    public function get_payment_method_script_handles_for_admin() {
        return $this->get_payment_method_script_handles();
    }
    
    /**
     * Get payment method data for frontend
     */
    public function get_payment_method_data() {
        if (!$this->gateway) {
            return array();
        }

        // Create API client instance
        $api_client = new PayOp_API_Client(
            $this->gateway->get_option('public_key'),
            $this->gateway->get_option('secret_key'),
            $this->gateway->get_option('jwt_token'),
            $this->gateway->get_option('debug_mode') === 'yes'
        );

        $payment_methods_manager = new PayOp_Payment_Methods($api_client);
        
        return array(
            'title' => $this->gateway->title,
            'description' => $this->gateway->description,
            'supports' => array('products'),
            'logo_url' => PAYOP_WC_PLUGIN_URL . 'assets/images/payop-logo.png',
            'payment_methods' => $this->get_available_payment_methods(),
            'config' => array(
                'public_key' => $this->gateway->get_option('public_key'),
                'supported_currencies' => $this->get_supported_currencies(),
                'debug_mode' => $this->gateway->get_option('debug_mode') === 'yes',
                'ajax_url' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('payop_payment_nonce')
            )
        );
    }
    
    /**
     * Process payment using Store API context
     */
    public function process_payment_with_context($context, $result) {
        if ($context->payment_method !== $this->name) {
            return;
        }
        
        try {
            $payment_data = $context->payment_data;
            $order = $context->order;
            
            // Extract PayOp-specific data
            $payop_method_id = $payment_data['payop_payment_method'] ?? '';
            $payop_customer_data = $payment_data['payop_customer_data'] ?? array();
            
            if (empty($payop_method_id)) {
                $result->set_status('failure');
                $result->set_payment_details(array(
                    'errorMessage' => __('Please select a payment method', 'payop-personal-gateway')
                ));
                return;
            }
            
            // Validate and sanitize customer data
            $sanitized_data = $this->gateway->sanitize_customer_data($payop_customer_data);
            
            // Create API client instance
            $api_client = new PayOp_API_Client(
                $this->gateway->get_option('public_key'),
                $this->gateway->get_option('secret_key'),
                $this->gateway->get_option('jwt_token'),
                $this->gateway->get_option('debug_mode') === 'yes'
            );

            // Create PayOp invoice
            $invoice_data = $this->prepare_invoice_data($order, $payop_method_id, $sanitized_data);
            $invoice_response = $api_client->create_invoice($invoice_data);
            
            if (!$invoice_response || $invoice_response['status'] !== 1) {
                $result->set_status('failure');
                $result->set_payment_details(array(
                    'errorMessage' => $invoice_response['message'] ?? __('Failed to create invoice', 'payop-personal-gateway')
                ));
                return;
            }
            
            $invoice_id = $invoice_response['data'];
            
            // Create checkout transaction
            $checkout_data = $this->prepare_checkout_data($invoice_id, $sanitized_data, $payop_method_id, $order);
            $checkout_response = $api_client->create_checkout($checkout_data);
            
            if (!$checkout_response || $checkout_response['status'] !== 1) {
                $result->set_status('failure');
                $result->set_payment_details(array(
                    'errorMessage' => $checkout_response['message'] ?? __('Failed to create checkout', 'payop-personal-gateway')
                ));
                return;
            }
            
            $checkout = $checkout_response['data'];
            
            // Store transaction data
            $this->store_transaction_data($order, $invoice_id, $checkout, $sanitized_data);
            
            // Update order status
            $order->update_status('pending', __('Awaiting PayOp payment', 'payop-personal-gateway'));
            
            // Get redirect URL
            $redirect_url = $this->get_payment_redirect_url($invoice_id, $checkout);

            $result->set_status('success');
            $result->set_payment_details(array(
                'payop_invoice_id' => $invoice_id,
                'payop_transaction_id' => $checkout['txid'] ?? ''
            ));
            $result->set_redirect_url($redirect_url);
            
        } catch (Exception $e) {
            $result->set_status('error');
            $result->set_payment_details(array(
                'errorMessage' => $e->getMessage()
            ));
        }
    }
    
    /**
     * Get available payment methods
     */
    private function get_available_payment_methods() {
        try {
            if (!$this->gateway) {
                return array();
            }

            // Create API client instance
            $api_client = new PayOp_API_Client(
                $this->gateway->get_option('public_key'),
                $this->gateway->get_option('secret_key'),
                $this->gateway->get_option('jwt_token'),
                $this->gateway->get_option('debug_mode') === 'yes'
            );

            $payment_methods_manager = new PayOp_Payment_Methods($api_client);
            $currency = get_woocommerce_currency();
            $country = WC()->customer ? WC()->customer->get_billing_country() : '';
            
            return $payment_methods_manager->get_available_methods($currency, $country);
        } catch (Exception $e) {
            return array();
        }
    }
    
    /**
     * Get supported currencies
     */
    private function get_supported_currencies() {
        return array('USD', 'EUR', 'GBP', 'PHP', 'CAD', 'AUD', 'BRL', 'DKK');
    }
    
    /**
     * Prepare invoice data for PayOp
     */
    private function prepare_invoice_data($order, $payment_method_id, $customer_data) {
        return array(
            'publicKey' => $this->gateway->get_option('public_key'),
            'order' => array(
                'id' => $order->get_order_number(),
                'amount' => number_format($order->get_total(), 2, '.', ''),
                'currency' => $order->get_currency(),
                'description' => sprintf(__('Order #%s', 'payop-personal-gateway'), $order->get_order_number()),
                'items' => $this->get_order_items($order)
            ),
            'payer' => array(
                'email' => $order->get_billing_email()
            ),
            'language' => 'en',
            'resultUrl' => add_query_arg(array(
                'invoiceId' => '{{invoiceId}}',
                'txid' => '{{txid}}'
            ), wc_get_checkout_url()),
            'failPath' => wc_get_cart_url()
        );
    }
    
    /**
     * Prepare checkout data
     */
    private function prepare_checkout_data($invoice_id, $customer_data, $payment_method_id, $order) {
        return array(
            'invoiceIdentifier' => $invoice_id,
            'customer' => array_merge(
                array(
                    'email' => $order->get_billing_email(),
                    'name' => trim($order->get_billing_first_name() . ' ' . $order->get_billing_last_name()),
                    'ip' => $this->get_customer_ip()
                ),
                $customer_data
            ),
            'paymentMethod' => $payment_method_id,
            'checkStatusUrl' => add_query_arg('payop_check', '{{txid}}', wc_get_checkout_url())
        );
    }
    
    /**
     * Get order items for PayOp
     */
    private function get_order_items($order) {
        $items = array();
        
        foreach ($order->get_items() as $item) {
            $items[] = array(
                'id' => $item->get_product_id(),
                'name' => $item->get_name(),
                'price' => number_format($item->get_total(), 2, '.', '')
            );
        }
        
        return $items;
    }
    
    /**
     * Store transaction data
     */
    private function store_transaction_data($order, $invoice_id, $checkout, $customer_data) {
        global $wpdb;
        
        $table = $wpdb->prefix . 'payop_transactions';
        
        $wpdb->insert(
            $table,
            array(
                'order_id' => $order->get_id(),
                'invoice_id' => $invoice_id,
                'transaction_id' => $checkout['txid'] ?? '',
                'payment_method_id' => $checkout['paymentMethod'] ?? '',
                'amount' => $order->get_total(),
                'currency' => $order->get_currency(),
                'status' => 'pending',
                'customer_data' => json_encode($customer_data),
                'api_response' => json_encode(array('invoice_id' => $invoice_id, 'checkout' => $checkout))
            ),
            array('%d', '%s', '%s', '%s', '%f', '%s', '%s', '%s', '%s')
        );
        
        // Store in order meta
        $order->update_meta_data('_payop_invoice_id', $invoice_id);
        $order->update_meta_data('_payop_transaction_id', $checkout['txid'] ?? '');
        $order->save();
    }
    
    /**
     * Get payment redirect URL
     */
    private function get_payment_redirect_url($invoice_id, $checkout) {
        if (isset($checkout['isSuccess']) && $checkout['isSuccess']) {
            try {
                // Create API client instance
                $api_client = new PayOp_API_Client(
                    $this->gateway->get_option('public_key'),
                    $this->gateway->get_option('secret_key'),
                    $this->gateway->get_option('jwt_token'),
                    $this->gateway->get_option('debug_mode') === 'yes'
                );

                $status_response = $api_client->check_invoice_status($invoice_id);
                
                if ($status_response && $status_response['status'] === 1) {
                    $status_data = $status_response['data'];
                    
                    if (isset($status_data['form']) && isset($status_data['form']['url'])) {
                        return $status_data['form']['url'];
                    }
                }
            } catch (Exception $e) {
                // Log error but continue
            }
        }
        
        return wc_get_checkout_url();
    }
    
    /**
     * Get customer IP address
     */
    private function get_customer_ip() {
        $ip_keys = array('HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'REMOTE_ADDR');
        
        foreach ($ip_keys as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                foreach (explode(',', $_SERVER[$key]) as $ip) {
                    $ip = trim($ip);
                    if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                        return $ip;
                    }
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
    }
}

// Initialize the blocks integration
new PayOp_Blocks_Integration();
