<?php
/**
 * PayOp Admin Interface
 */

if (!defined('ABSPATH')) {
    exit;
}

class PayOp_Admin {
    
    private $api_client;
    private $payment_methods_manager;
    
    /**
     * Constructor
     */
    public function __construct() {
        $gateway_settings = get_option('woocommerce_payop_settings', array());
        
        $this->api_client = new PayOp_API_Client(
            $gateway_settings['public_key'] ?? '',
            $gateway_settings['secret_key'] ?? '',
            $gateway_settings['jwt_token'] ?? '',
            ($gateway_settings['debug_mode'] ?? 'no') === 'yes'
        );
        
        $this->payment_methods_manager = new PayOp_Payment_Methods($this->api_client);
        
        // Admin hooks
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
        add_action('wp_ajax_payop_test_connection', array($this, 'ajax_test_connection'));
        add_action('wp_ajax_payop_sync_payment_methods', array($this, 'ajax_sync_payment_methods'));
        add_action('wp_ajax_payop_toggle_payment_method', array($this, 'ajax_toggle_payment_method'));
        add_action('wp_ajax_payop_save_config', array($this, 'ajax_save_config'));
    }
    
    /**
     * Add admin menu
     */
    public function add_menu() {
        add_submenu_page(
            'woocommerce',
            __('PayOp Payment Methods', 'payop-personal-gateway'),
            __('PayOp Methods', 'payop-personal-gateway'),
            'manage_woocommerce',
            'payop-payment-methods',
            array($this, 'render_payment_methods_page')
        );
    }
    
    /**
     * Enqueue admin scripts
     */
    public function enqueue_admin_scripts($hook) {
        if ($hook === 'woocommerce_page_payop-payment-methods') {
            wp_enqueue_script(
                'payop-admin',
                PAYOP_WC_PLUGIN_URL . 'assets/js/payop-admin.js',
                array('jquery'),
                PAYOP_WC_VERSION,
                true
            );
            
            wp_localize_script('payop-admin', 'payop_admin_params', array(
                'ajax_url' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('payop_admin_nonce'),
                'messages' => array(
                    'confirm_refresh' => __('This will refresh payment methods from PayOp API. Continue?', 'payop-personal-gateway'),
                    'testing_connection' => __('Testing connection...', 'payop-personal-gateway'),
                    'refreshing_methods' => __('Refreshing payment methods...', 'payop-personal-gateway'),
                    'sending_test_ipn' => __('Sending test IPN...', 'payop-personal-gateway')
                )
            ));
            
            wp_enqueue_style(
                'payop-admin',
                PAYOP_WC_PLUGIN_URL . 'assets/css/payop-admin.css',
                array(),
                PAYOP_WC_VERSION
            );
        }
    }
    
    /**
     * Render payment methods page
     */
    public function render_payment_methods_page() {
        $stats = $this->payment_methods_manager->get_method_stats();
        $gateway_settings = get_option('woocommerce_payop_settings', array());
        $is_configured = !empty($gateway_settings['public_key']) && !empty($gateway_settings['secret_key']);
        
        ?>
        <div class="wrap">
            <h1><?php _e('PayOp Payment Methods', 'payop-personal-gateway'); ?></h1>
            
            <?php if (!$is_configured): ?>
                <div class="notice notice-warning">
                    <p>
                        <?php _e('PayOp gateway is not configured. Please configure your API credentials in', 'payop-personal-gateway'); ?>
                        <a href="<?php echo admin_url('admin.php?page=wc-settings&tab=checkout&section=payop'); ?>">
                            <?php _e('WooCommerce Settings', 'payop-personal-gateway'); ?>
                        </a>
                    </p>
                </div>
            <?php endif; ?>
            
            <div class="payop-admin-header">
                <div class="payop-stats">
                    <div class="stat-box">
                        <h3><?php echo $stats['total_methods']; ?></h3>
                        <p><?php _e('Total Methods', 'payop-personal-gateway'); ?></p>
                    </div>
                    <div class="stat-box">
                        <h3><?php echo $stats['enabled_methods']; ?></h3>
                        <p><?php _e('Enabled Methods', 'payop-personal-gateway'); ?></p>
                    </div>
                    <div class="stat-box">
                        <h3><?php echo $stats['total_transactions']; ?></h3>
                        <p><?php _e('Total Transactions', 'payop-personal-gateway'); ?></p>
                    </div>
                    <div class="stat-box">
                        <h3><?php echo $stats['successful_transactions']; ?></h3>
                        <p><?php _e('Successful Transactions', 'payop-personal-gateway'); ?></p>
                    </div>
                </div>
                
                <div class="payop-actions">
                    <button type="button" class="button" id="test-connection">
                        <?php _e('Test Connection', 'payop-personal-gateway'); ?>
                    </button>
                    <button type="button" class="button" id="refresh-methods">
                        <?php _e('Refresh Methods', 'payop-personal-gateway'); ?>
                    </button>
                </div>
            </div>
            
            <div id="payop-notices"></div>
            
            <?php if ($stats['last_sync']): ?>
                <p class="description">
                    <?php 
                    printf(
                        __('Last synchronized: %s', 'payop-personal-gateway'),
                        date_i18n(get_option('date_format') . ' ' . get_option('time_format'), $stats['last_sync'])
                    );
                    ?>
                </p>
            <?php endif; ?>
            
            <?php if ($is_configured): ?>
                <?php $this->render_payment_methods_table(); ?>
                <?php $this->render_transaction_logs(); ?>
            <?php endif; ?>
        </div>
        <?php
    }
    
    /**
     * Render payment methods table
     */
    private function render_payment_methods_table() {
        global $wpdb;
        
        $table = $wpdb->prefix . 'payop_payment_methods';
        $methods = $wpdb->get_results("SELECT * FROM $table ORDER BY display_order ASC, title ASC", ARRAY_A);
        
        ?>
        <h2><?php _e('Payment Methods', 'payop-personal-gateway'); ?></h2>
        
        <?php if (empty($methods)): ?>
            <p><?php _e('No payment methods found. Click "Refresh Methods" to load them from PayOp API.', 'payop-personal-gateway'); ?></p>
        <?php else: ?>
            <table class="wp-list-table widefat fixed striped">
                <thead>
                    <tr>
                        <th><?php _e('Logo', 'payop-personal-gateway'); ?></th>
                        <th><?php _e('Title', 'payop-personal-gateway'); ?></th>
                        <th><?php _e('Type', 'payop-personal-gateway'); ?></th>
                        <th><?php _e('Currencies', 'payop-personal-gateway'); ?></th>
                        <th><?php _e('Countries', 'payop-personal-gateway'); ?></th>
                        <th><?php _e('Status', 'payop-personal-gateway'); ?></th>
                        <th><?php _e('Actions', 'payop-personal-gateway'); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($methods as $method): ?>
                        <tr>
                            <td>
                                <?php if ($method['logo']): ?>
                                    <img src="<?php echo esc_url($method['logo']); ?>" alt="<?php echo esc_attr($method['title']); ?>" style="max-width: 40px; max-height: 20px;" />
                                <?php endif; ?>
                            </td>
                            <td>
                                <strong><?php echo esc_html($method['title']); ?></strong>
                                <br><small>ID: <?php echo $method['identifier']; ?></small>
                            </td>
                            <td><?php echo esc_html($method['type']); ?></td>
                            <td>
                                <?php 
                                $currencies = json_decode($method['currencies'], true);
                                if ($currencies) {
                                    echo esc_html(implode(', ', array_slice($currencies, 0, 3)));
                                    if (count($currencies) > 3) {
                                        echo ' <small>(+' . (count($currencies) - 3) . ' more)</small>';
                                    }
                                }
                                ?>
                            </td>
                            <td>
                                <?php 
                                $countries = json_decode($method['countries'], true);
                                if ($countries) {
                                    echo esc_html(implode(', ', array_slice($countries, 0, 3)));
                                    if (count($countries) > 3) {
                                        echo ' <small>(+' . (count($countries) - 3) . ' more)</small>';
                                    }
                                } else {
                                    echo __('All countries', 'payop-personal-gateway');
                                }
                                ?>
                            </td>
                            <td>
                                <span class="status-<?php echo $method['is_enabled'] ? 'enabled' : 'disabled'; ?>">
                                    <?php echo $method['is_enabled'] ? __('Enabled', 'payop-personal-gateway') : __('Disabled', 'payop-personal-gateway'); ?>
                                </span>
                            </td>
                            <td>
                                <button type="button" 
                                        class="button toggle-method" 
                                        data-method-id="<?php echo $method['identifier']; ?>"
                                        data-current-status="<?php echo $method['is_enabled']; ?>">
                                    <?php echo $method['is_enabled'] ? __('Disable', 'payop-personal-gateway') : __('Enable', 'payop-personal-gateway'); ?>
                                </button>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        <?php endif; ?>
        <?php
    }
    
    /**
     * Render transaction logs
     */
    private function render_transaction_logs() {
        global $wpdb;
        
        $table = $wpdb->prefix . 'payop_transactions';
        $transactions = $wpdb->get_results("
            SELECT * FROM $table 
            ORDER BY created_at DESC 
            LIMIT 20
        ", ARRAY_A);
        
        ?>
        <h2><?php _e('Recent Transactions', 'payop-personal-gateway'); ?></h2>
        
        <?php if (empty($transactions)): ?>
            <p><?php _e('No transactions found.', 'payop-personal-gateway'); ?></p>
        <?php else: ?>
            <table class="wp-list-table widefat fixed striped">
                <thead>
                    <tr>
                        <th><?php _e('Order ID', 'payop-personal-gateway'); ?></th>
                        <th><?php _e('Invoice ID', 'payop-personal-gateway'); ?></th>
                        <th><?php _e('Amount', 'payop-personal-gateway'); ?></th>
                        <th><?php _e('Status', 'payop-personal-gateway'); ?></th>
                        <th><?php _e('Date', 'payop-personal-gateway'); ?></th>
                        <th><?php _e('Actions', 'payop-personal-gateway'); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($transactions as $transaction): ?>
                        <tr>
                            <td>
                                <a href="<?php echo admin_url('post.php?post=' . $transaction['order_id'] . '&action=edit'); ?>">
                                    #<?php echo $transaction['order_id']; ?>
                                </a>
                            </td>
                            <td><?php echo esc_html($transaction['invoice_id']); ?></td>
                            <td><?php echo $transaction['amount'] . ' ' . $transaction['currency']; ?></td>
                            <td>
                                <span class="status-<?php echo $transaction['status']; ?>">
                                    <?php echo esc_html(ucfirst($transaction['status'])); ?>
                                </span>
                            </td>
                            <td><?php echo date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($transaction['created_at'])); ?></td>
                            <td>
                                <?php if ($transaction['status'] === 'pending'): ?>
                                    <button type="button" 
                                            class="button button-small send-test-ipn" 
                                            data-order-id="<?php echo $transaction['order_id']; ?>">
                                        <?php _e('Test IPN', 'payop-personal-gateway'); ?>
                                    </button>
                                <?php endif; ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        <?php endif; ?>
        <?php
    }
    
    /**
     * AJAX: Test connection
     */
    public function ajax_test_connection() {
        check_ajax_referer('payop_admin_nonce', 'nonce');
        
        if (!current_user_can('manage_woocommerce')) {
            wp_send_json_error(__('Insufficient permissions.', 'payop-personal-gateway'));
        }
        
        $result = $this->api_client->test_connection();
        
        if ($result['success']) {
            wp_send_json_success(array(
                'message' => __('Connection successful! PayOp API is reachable.', 'payop-personal-gateway'),
                'methods_count' => count($result['data']['data'] ?? array())
            ));
        } else {
            wp_send_json_error($result['message']);
        }
    }
    
    /**
     * AJAX: Refresh payment methods
     */
    public function ajax_sync_payment_methods() {
        check_ajax_referer('payop_admin_nonce', 'nonce');
        
        if (!current_user_can('manage_woocommerce')) {
            wp_send_json_error(__('Insufficient permissions.', 'payop-personal-gateway'));
        }
        
        try {
            $methods = $this->payment_methods_manager->refresh_methods();
            wp_send_json_success(array(
                'message' => sprintf(__('Successfully refreshed %d payment methods.', 'payop-personal-gateway'), count($methods)),
                'count' => count($methods)
            ));
        } catch (Exception $e) {
            wp_send_json_error($e->getMessage());
        }
    }
    
    /**
     * AJAX: Toggle payment method
     */
    public function ajax_toggle_method() {
        check_ajax_referer('payop_admin_nonce', 'nonce');
        
        if (!current_user_can('manage_woocommerce')) {
            wp_send_json_error(__('Insufficient permissions.', 'payop-personal-gateway'));
        }
        
        $method_id = intval($_POST['method_id']);
        $current_status = intval($_POST['current_status']);
        $new_status = !$current_status;
        
        $result = $this->payment_methods_manager->toggle_method($method_id, $new_status);
        
        if ($result !== false) {
            wp_send_json_success(array(
                'message' => $new_status ? __('Payment method enabled.', 'payop-personal-gateway') : __('Payment method disabled.', 'payop-personal-gateway'),
                'new_status' => $new_status
            ));
        } else {
            wp_send_json_error(__('Failed to update payment method status.', 'payop-personal-gateway'));
        }
    }
    
    /**
     * AJAX: Send test IPN
     */
    public function ajax_send_test_ipn() {
        check_ajax_referer('payop_admin_nonce', 'nonce');
        
        if (!current_user_can('manage_woocommerce')) {
            wp_send_json_error(__('Insufficient permissions.', 'payop-personal-gateway'));
        }
        
        $order_id = intval($_POST['order_id']);
        
        $ipn_handler = new PayOp_IPN_Handler($this->api_client);
        $result = $ipn_handler->send_test_ipn($order_id);
        
        if ($result) {
            wp_send_json_success(__('Test IPN sent successfully.', 'payop-personal-gateway'));
        } else {
            wp_send_json_error(__('Failed to send test IPN.', 'payop-personal-gateway'));
        }
    }
    
    /**
     * AJAX: Sync payment methods
     */
    public function ajax_sync_payment_methods() {
        check_ajax_referer('payop_admin_nonce', 'nonce');
        
        try {
            $result = $this->payment_methods_manager->refresh_payment_methods();
            
            if ($result) {
                wp_send_json_success(array(
                    'message' => __('Payment methods synchronized successfully.', 'payop-personal-gateway')
                ));
            } else {
                wp_send_json_error(array(
                    'message' => __('Failed to sync payment methods.', 'payop-personal-gateway')
                ));
            }
        } catch (Exception $e) {
            wp_send_json_error(array(
                'message' => $e->getMessage()
            ));
        }
    }
    
    /**
     * AJAX: Toggle payment method
     */
    public function ajax_toggle_payment_method() {
        check_ajax_referer('payop_admin_nonce', 'nonce');
        
        $method_id = sanitize_text_field($_POST['method_id'] ?? '');
        $enabled = sanitize_text_field($_POST['enabled'] ?? '0');
        
        if (empty($method_id)) {
            wp_send_json_error(array('message' => 'Method ID is required'));
        }
        
        try {
            $result = $this->payment_methods_manager->toggle_method($method_id, $enabled === '1');
            
            if ($result) {
                wp_send_json_success(array(
                    'message' => __('Payment method updated successfully.', 'payop-personal-gateway')
                ));
            } else {
                wp_send_json_error(array(
                    'message' => __('Failed to update payment method.', 'payop-personal-gateway')
                ));
            }
        } catch (Exception $e) {
            wp_send_json_error(array(
                'message' => $e->getMessage()
            ));
        }
    }
    
    /**
     * AJAX: Save configuration
     */
    public function ajax_save_config() {
        check_ajax_referer('payop_admin_nonce', 'nonce');
        
        // This would handle saving gateway configuration
        // For now, we just return success since settings are handled by WooCommerce
        wp_send_json_success(array(
            'message' => __('Configuration saved successfully.', 'payop-personal-gateway')
        ));
    }
}
